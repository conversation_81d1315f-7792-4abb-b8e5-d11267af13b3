-- MySQL dump 10.13  Distrib 8.0.42, for Linux (x86_64)
--
-- Host: 127.0.0.1    Database: anxinlaDB
-- ------------------------------------------------------
-- Server version	8.0.42-0ubuntu0.24.04.2

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `Admin`
--

DROP TABLE IF EXISTS `Admin`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `Admin` (
  `admin_id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(8) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`admin_id`),
  UNIQUE KEY `username` (`username`),
  CONSTRAINT `Admin_chk_1` CHECK (regexp_like(`password`,_latin1'^[a-zA-Z0-9]{8}$'))
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `Admin`
--

LOCK TABLES `Admin` WRITE;
/*!40000 ALTER TABLE `Admin` DISABLE KEYS */;
INSERT INTO `Admin` VALUES (1,'admin','Ab1c2d3e','2025-07-04 03:31:30','2025-07-04 03:31:30');
/*!40000 ALTER TABLE `Admin` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `Device`
--

DROP TABLE IF EXISTS `Device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `Device` (
  `device_id` varchar(30) NOT NULL,
  `device_type` int NOT NULL,
  `storage_location` int NOT NULL,
  `description` text,
  `status` enum('available','outbound') DEFAULT 'available' COMMENT '设备状态',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`device_id`),
  KEY `device_type` (`device_type`),
  KEY `storage_location` (`storage_location`),
  CONSTRAINT `Device_ibfk_1` FOREIGN KEY (`device_type`) REFERENCES `DeviceType` (`type_id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `Device_ibfk_2` FOREIGN KEY (`storage_location`) REFERENCES `StorageLocation` (`location_id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `Device`
--

LOCK TABLES `Device` WRITE;
/*!40000 ALTER TABLE `Device` DISABLE KEYS */;
INSERT INTO `Device` VALUES ('B01220400854',1009,2,'','available','2025-07-18 02:36:42','2025-07-18 02:36:42'),('B01220400947',1009,18,'','available','2025-07-18 09:18:00','2025-07-18 09:18:00'),('B01220401208',1009,2,'','available','2025-07-18 02:40:02','2025-07-18 02:40:02'),('B01220401399',1008,1,'','available','2025-07-18 02:02:19','2025-07-18 02:02:19'),('B01220401570',1008,6,'','available','2025-07-18 03:41:50','2025-07-18 03:41:50'),('B01220401912',1009,2,'','available','2025-07-18 02:31:01','2025-07-18 02:31:01'),('B01220401918',1009,5,'','available','2025-07-18 03:29:06','2025-07-18 03:29:06'),('B01220401920',1009,8,'','available','2025-07-18 05:56:58','2025-07-18 05:56:58'),('B01220401929',1008,21,'','available','2025-07-27 10:35:02','2025-07-27 10:35:02'),('B01220402045',1009,2,'','available','2025-07-18 02:35:48','2025-07-18 02:35:48'),('B012250131501',1009,2,'','available','2025-07-18 02:38:56','2025-07-18 02:38:56'),('B012251133009',1009,2,'','available','2025-07-18 02:32:18','2025-07-18 02:32:18'),('B012308044550',1011,2002,'','available','2025-07-18 07:09:19','2025-07-18 07:09:19'),('B01231101490',1009,20,'','available','2025-07-18 09:42:38','2025-07-22 15:02:49'),('B01233101246',1009,18,'','available','2025-07-18 09:16:12','2025-07-18 09:16:12'),('B01233101755',1009,5,'','available','2025-07-18 03:35:33','2025-07-18 03:35:33'),('B01233101759',1008,6,'','available','2025-07-18 03:45:02','2025-07-18 03:45:02'),('B01233101766',1009,5,'','available','2025-07-18 03:27:40','2025-07-18 03:27:40'),('B01233101781',1008,21,'','available','2025-07-27 10:34:52','2025-07-27 10:34:52'),('B01233102177',1009,2,'','available','2025-07-18 02:33:19','2025-07-18 02:33:19'),('B01233800014',1009,18,'','available','2025-07-18 09:16:34','2025-07-18 09:16:34'),('B01233800104',1009,2,'','available','2025-07-18 02:37:58','2025-07-18 02:37:58'),('B01233801421',1009,8,'','available','2025-07-18 05:55:17','2025-07-18 05:55:17'),('B01233803072',1009,8,'','available','2025-07-18 05:56:05','2025-07-18 05:56:05'),('B01234602731',1008,1,'','available','2025-07-18 02:20:33','2025-07-18 02:20:33'),('B01234604628',1009,8,'','available','2025-07-18 05:55:42','2025-07-18 05:55:42'),('B01234604629',1009,5,'','available','2025-07-18 03:31:11','2025-07-18 03:31:11'),('B01234604902',1009,20,'','available','2025-07-18 09:41:47','2025-07-18 09:41:47'),('B01234604911',1009,8,'','available','2025-07-18 05:54:50','2025-07-18 05:54:50'),('B01234604958',1009,2,'','available','2025-07-18 02:42:46','2025-07-18 02:42:46'),('B01243000088',1009,5,'','available','2025-07-18 03:30:40','2025-07-18 03:30:40'),('B01243000096',1009,5,'','available','2025-07-18 03:28:16','2025-07-18 03:28:16'),('B01243000160',1009,5,'','available','2025-07-18 03:34:37','2025-07-18 03:34:37'),('B02223215274',1008,5,'','available','2025-07-18 03:26:43','2025-07-18 03:26:43'),('B02224407907',1009,15,'','available','2025-07-18 07:56:09','2025-07-18 07:56:09'),('B02224407910',1009,15,'','available','2025-07-18 07:58:23','2025-07-18 07:58:23'),('B02224407912',1009,15,'','available','2025-07-18 07:57:06','2025-07-18 07:57:06'),('B02224407915',1009,15,'','available','2025-07-18 07:55:40','2025-07-18 07:55:40'),('B02224407921',1009,15,'','available','2025-07-18 07:55:53','2025-07-18 07:55:53'),('B02224408037',1009,15,'','available','2025-07-18 07:57:54','2025-07-18 07:57:54'),('B02224408553',1009,15,'','available','2025-07-18 07:54:05','2025-07-18 07:54:05'),('B02224408563',1009,15,'','available','2025-07-18 07:56:24','2025-07-18 07:56:24'),('B02224408566',1009,15,'','available','2025-07-18 07:54:53','2025-07-18 07:54:53'),('B02224408754',1009,15,'','available','2025-07-18 07:55:23','2025-07-18 07:55:23'),('B02224408757',1009,15,'','available','2025-07-18 07:58:11','2025-07-18 07:58:11'),('B02224408767',1009,15,'','available','2025-07-18 07:57:19','2025-07-18 07:57:19'),('B02224408774',1009,15,'','available','2025-07-18 07:55:09','2025-07-18 07:55:09'),('B02224408776',1009,15,'','available','2025-07-18 07:58:43','2025-07-18 07:58:43'),('B02224408839',1009,15,'','available','2025-07-18 07:56:53','2025-07-18 07:56:53'),('B02224408937',1009,15,'','available','2025-07-18 07:58:55','2025-07-18 07:58:55'),('B022246024925',1009,18,'','available','2025-07-18 09:15:43','2025-07-18 09:15:43'),('B022246024943',1009,18,'','available','2025-07-18 09:16:51','2025-07-18 09:16:51'),('B022246024969',1009,18,'','available','2025-07-18 09:18:39','2025-07-18 09:18:39'),('B022246024985',1008,1,'','available','2025-07-18 02:19:33','2025-07-18 02:19:33'),('B022246024988',1009,18,'','available','2025-07-18 09:17:38','2025-07-18 09:17:38'),('B022246025168',1011,2002,'','available','2025-07-18 07:15:22','2025-07-18 07:15:22'),('B022246025238',1009,18,'','available','2025-07-18 09:14:59','2025-07-18 09:14:59'),('B03233000852',1009,18,'','available','2025-07-18 09:17:09','2025-07-18 09:17:09'),('B03233700293',1009,2,'','available','2025-07-18 02:41:22','2025-07-18 02:41:22'),('B03241110468',1008,1,'','available','2025-07-18 02:21:54','2025-07-18 02:21:54'),('B03241110470',1008,16,'','available','2025-07-18 08:08:02','2025-07-18 08:08:02'),('B03241110474',1007,19,'','available','2025-07-18 09:26:20','2025-07-18 09:26:20'),('B03241110479',1008,6,'','available','2025-07-18 03:44:40','2025-07-18 03:44:40'),('B03241110484',1008,1,'','available','2025-07-18 02:18:37','2025-07-18 02:18:37'),('B03241110499',1008,1,'','available','2025-07-18 02:00:46','2025-07-18 02:00:46'),('B03241110623',1008,1,'','available','2025-07-18 02:03:21','2025-07-18 02:03:21'),('B03241110625',1008,6,'','available','2025-07-18 03:42:40','2025-07-18 03:42:40'),('B03241110692',1008,6,'','available','2025-07-18 03:42:15','2025-07-18 03:42:15'),('B032436B0401',1008,6,'','available','2025-07-18 03:40:35','2025-07-18 03:40:35'),('B032436B0860',1008,6,'','available','2025-07-18 03:41:03','2025-07-18 03:41:03'),('B032436B0871',1008,1,'','available','2025-07-18 02:21:11','2025-07-18 02:21:11'),('B032436B0903',1008,1,'','available','2025-07-18 02:01:28','2025-07-18 02:01:28'),('B032436B1302',1008,6,'','available','2025-07-18 03:44:07','2025-07-18 03:44:07'),('B032441B0093',1008,6,'','available','2025-07-18 03:43:17','2025-07-18 03:43:17'),('B032441B0363',1008,1,'','available','2025-07-18 02:04:03','2025-07-18 02:04:03'),('B032441B0845',1008,6,'','available','2025-07-18 03:43:37','2025-07-18 03:43:37'),('B032441B1473',1008,16,'','available','2025-07-18 08:08:16','2025-07-18 08:08:16'),('BSJ01245001795069',1008,21,'','available','2025-07-27 10:35:15','2025-07-27 10:35:15'),('BSJ01245001819983',1009,20,'','available','2025-07-18 09:41:27','2025-07-18 09:41:27'),('BSJ01252303680095',1012,13,'','available','2025-07-18 07:39:00','2025-07-18 07:39:00'),('BSJ01252303680096',1012,13,'','available','2025-07-18 07:40:05','2025-07-18 07:40:05'),('BSJ01252303680261',1012,13,'','available','2025-07-18 07:37:57','2025-07-18 07:37:57'),('BSJ01252303680311',1012,13,'','available','2025-07-18 07:37:27','2025-07-18 07:37:27'),('BSJ01252303680322',1012,13,'','available','2025-07-18 07:36:38','2025-07-18 07:36:38'),('BSJ01252303680325',1012,13,'','available','2025-07-18 07:35:36','2025-07-18 07:35:36'),('BSJ01252303680326',1012,13,'','available','2025-07-18 07:39:16','2025-07-18 07:39:16'),('BSJ01252303680339',1012,13,'','available','2025-07-18 07:35:50','2025-07-18 07:35:50'),('BSJ01252303680345',1012,13,'','available','2025-07-18 07:37:11','2025-07-18 07:37:11'),('BSJ01252303680350',1012,13,'','available','2025-07-18 07:36:16','2025-07-18 07:36:16'),('BSJ01252303680868',1012,13,'','available','2025-07-18 07:37:41','2025-07-18 07:37:41'),('BSJ01252303707391',1012,13,'','available','2025-07-18 07:39:46','2025-07-18 07:39:46'),('BSJ01252303707449',1012,13,'','available','2025-07-18 07:36:59','2025-07-18 07:36:59'),('BSJ01252303707837',1012,13,'','available','2025-07-18 07:36:01','2025-07-18 07:36:01'),('BSJ01252303707844',1012,13,'','available','2025-07-18 07:39:28','2025-07-18 07:39:28'),('BSJ03251302876469',1008,16,'','available','2025-07-18 08:06:45','2025-07-18 08:06:45'),('BSJ03251302876549',1008,16,'','available','2025-07-18 08:06:31','2025-07-18 08:06:31'),('C01233705882',1009,8,'','available','2025-07-18 05:58:55','2025-07-18 05:58:55'),('C01234301962',1011,2002,'','available','2025-07-18 07:13:17','2025-07-18 07:13:17'),('C012439Y0186',1013,21,'','available','2025-07-27 10:35:31','2025-07-27 10:35:31'),('C012439Y0193',1009,8,'','available','2025-07-18 06:01:48','2025-07-18 06:01:48'),('C012439Y0222',1013,21,'','available','2025-07-27 10:34:16','2025-07-27 10:34:16'),('C022243011883',1011,2002,'','available','2025-07-18 07:10:44','2025-07-18 07:10:44'),('C022243011936',1009,8,'','available','2025-07-18 06:00:48','2025-07-18 06:00:48'),('C022243013637',1009,8,'','available','2025-07-18 06:00:01','2025-07-18 06:00:01'),('C03231200928',1009,18,'','available','2025-07-18 09:15:24','2025-07-18 09:15:24'),('C03231204230',1009,5,'700箱货设备','available','2025-07-18 03:30:02','2025-07-18 03:30:02'),('C03240301439',1009,8,'','available','2025-07-18 05:59:27','2025-07-18 05:59:27'),('C03240301440',1013,16,'','available','2025-07-18 08:07:11','2025-07-18 08:07:11'),('C03242200259',1013,21,'','available','2025-07-27 10:34:34','2025-07-27 10:34:34'),('C03242200743',1011,2002,'','available','2025-07-18 07:10:13','2025-07-18 07:10:13'),('C03242200751',1011,2002,'','available','2025-07-18 07:15:10','2025-07-18 07:15:10'),('CHT03252303682080',1013,14,'','available','2025-07-18 07:47:52','2025-07-18 07:47:52'),('CHT03252303682083',1013,14,'','available','2025-07-18 07:47:39','2025-07-18 07:47:39'),('CHT03252303682086',1013,14,'','available','2025-07-18 07:48:35','2025-07-18 07:48:35'),('CHT03252303682089',1013,14,'','available','2025-07-18 07:48:03','2025-07-18 07:48:03'),('CHT03252303682090',1013,14,'','available','2025-07-18 07:48:16','2025-07-18 07:48:16'),('CHT03252303682091',1013,14,'','available','2025-07-18 07:49:05','2025-07-18 07:49:05'),('CHT03252303682096',1013,14,'','available','2025-07-18 07:48:47','2025-07-18 07:48:47'),('YC101212100473',1011,2002,'','available','2025-07-18 07:31:36','2025-07-18 07:31:36'),('YC101212100622',1010,7,'','available','2025-07-18 05:36:37','2025-07-18 05:36:37'),('YJ101212703570',1007,19,'','available','2025-07-27 10:31:34','2025-07-27 10:31:34'),('YJ101213001255',1010,7,'','available','2025-07-18 05:38:32','2025-07-18 05:38:32'),('YJ101213001527',1007,19,'','available','2025-07-18 09:23:00','2025-07-18 09:23:00'),('ysx20210804171530900',1010,3,'','available','2025-07-18 02:50:04','2025-07-18 02:50:04'),('ysx20210809151740673',1007,19,'','available','2025-07-18 09:24:42','2025-07-18 09:24:42'),('ysx20210813182645920',1010,20,'','available','2025-07-18 09:39:45','2025-07-18 09:39:45'),('ysx20210819101611330',1010,7,'','available','2025-07-18 05:36:57','2025-07-18 05:36:57'),('YSX20210823124951410',1011,2002,'','available','2025-07-18 07:31:12','2025-07-18 07:31:12'),('YSX20210823125058897',1007,20,'','available','2025-07-18 09:40:00','2025-07-18 09:40:00'),('YSX20210825123857000',1010,4,'','available','2025-07-18 03:08:19','2025-07-18 03:08:19'),('YSX20210825123943370',1007,19,'','available','2025-07-18 09:26:46','2025-07-18 09:26:46'),('YSX20210904170139100',1010,7,'','available','2025-07-18 05:33:00','2025-07-18 05:33:00'),('YSX20210904171056657',1010,20,'','available','2025-07-18 09:42:11','2025-07-18 09:42:11'),('YSX20210918202601150',1010,3,'','available','2025-07-18 02:48:45','2025-07-18 02:48:45'),('YSX20210922101244597',1011,2002,'','available','2025-07-18 07:24:31','2025-07-18 07:24:31'),('YSX20211026135636570',1010,4,'','available','2025-07-18 03:15:10','2025-07-18 03:15:10'),('YSX20211026135647400',1010,4,'','available','2025-07-18 03:19:44','2025-07-18 03:19:44'),('YSX20211026135651500',1010,7,'','available','2025-07-18 05:33:40','2025-07-18 05:33:40'),('YSX20211026135724973',1010,7,'','available','2025-07-18 05:34:21','2025-07-18 05:34:21'),('YSX20211026135736240',1011,2002,'','available','2025-07-18 07:31:01','2025-07-18 07:31:01'),('YSX20211026135749717',1009,5,'','available','2025-07-18 03:32:45','2025-07-18 03:32:45'),('YSX20211026135800060',1010,3,'','available','2025-07-18 02:56:31','2025-07-18 02:56:31'),('YSX20211026135806163',1007,20,'','available','2025-07-18 09:41:03','2025-07-18 09:41:03'),('YSX20211026135809007',1010,4,'','available','2025-07-18 03:19:19','2025-07-18 03:19:19'),('YSX20211026135809367',1010,4,'','available','2025-07-18 03:18:27','2025-07-18 03:18:27'),('YSX20211026135813800',1010,7,'','available','2025-07-18 05:44:20','2025-07-18 05:44:20'),('YSX20211026135827453',1011,2002,'','available','2025-07-18 07:09:36','2025-07-18 07:09:36'),('YSX20211026135828567',1007,19,'','available','2025-07-27 10:25:55','2025-07-27 10:25:55'),('YSX20211026135838333',1010,4,'','available','2025-07-18 03:17:34','2025-07-18 03:17:34'),('YSX20211216085758990',1011,2002,'','available','2025-07-18 07:11:48','2025-07-18 07:11:48'),('YSX20211216085852740',1007,19,'','available','2025-07-18 09:23:25','2025-07-18 09:23:25'),('YSX20211216085919827',1007,19,'','available','2025-07-27 10:27:23','2025-07-27 10:27:23'),('YSX20211216085941917',1007,19,'','available','2025-07-18 09:28:05','2025-07-18 09:28:05'),('YSX20211216085944887',1007,17,'','available','2025-07-18 08:17:42','2025-07-18 08:17:42'),('YSX20211216085956757',1010,7,'','available','2025-07-18 05:35:30','2025-07-18 05:35:30'),('YSX20211220143901983',1007,19,'','available','2025-07-27 10:30:56','2025-07-27 10:30:56'),('YSX20211220143907623',1010,7,'','available','2025-07-18 05:43:31','2025-07-18 05:43:31'),('YSX20211220143911220',1010,4,'','available','2025-07-18 03:18:54','2025-07-18 03:18:54'),('YSX20211220143913917',1010,3,'','available','2025-07-18 02:55:08','2025-07-18 02:55:08'),('YSX20211220143957250',1011,2002,'','available','2025-07-18 07:11:17','2025-07-18 07:11:19'),('YSX20211220144007523',1010,4,'','available','2025-07-18 03:16:12','2025-07-18 03:16:12'),('YSX20211220144007877',1010,7,'','available','2025-07-18 05:38:10','2025-07-18 05:38:10'),('YSX20211220144008560',1010,4,'','available','2025-07-18 03:16:53','2025-07-18 03:16:53'),('YSX20211220144008907',1010,20,'','available','2025-07-18 09:40:47','2025-07-18 09:40:47'),('YSX20211229143538940',1010,3,'','available','2025-07-18 02:54:36','2025-07-18 02:54:36'),('YSX20220105194322330',1010,3,'','available','2025-07-18 02:49:24','2025-07-18 02:49:24'),('YSX20220105194322410',1010,3,'','available','2025-07-18 02:57:16','2025-07-18 02:57:16'),('YSX20220105194326697',1007,19,'','available','2025-07-18 09:27:36','2025-07-18 09:27:36'),('YSX20220105194327753',1010,3,'','available','2025-07-18 02:55:38','2025-07-18 02:55:38'),('YSX20220105194331007',1007,20,'','available','2025-07-18 09:40:31','2025-07-18 09:40:31'),('YSX20220105194358933',1010,3,'','available','2025-07-18 02:58:50','2025-07-18 02:58:50'),('YSX20220105194400633',1011,2002,'','available','2025-07-18 07:09:49','2025-07-18 07:09:49'),('YSX20220112100513540',1007,19,'','available','2025-07-27 10:26:57','2025-07-27 10:26:57'),('YSX20220112100554967',1010,3,'','available','2025-07-18 02:59:53','2025-07-18 02:59:53'),('YSX20220112100905687',1011,2002,'','available','2025-07-18 07:12:48','2025-07-18 07:12:48'),('YSX20220303090808850',1010,4,'','available','2025-07-18 03:14:40','2025-07-18 03:14:40'),('Z01231306742',1007,20,'','available','2025-07-18 09:40:17','2025-07-18 09:40:17'),('Z01231703614',1007,9,'','available','2025-07-18 06:14:43','2025-07-18 06:14:43'),('Z01234501645',1007,9,'','available','2025-07-18 06:10:40','2025-07-18 06:10:40'),('Z01234503866',1007,12,'','available','2025-07-18 06:52:45','2025-07-18 06:52:45'),('Z01234504392',1007,11,'','available','2025-07-18 06:37:40','2025-07-18 06:37:40'),('Z01234504428',1007,11,'','available','2025-07-18 06:38:27','2025-07-18 06:38:27'),('ZL20210826180623373',1007,12,'','available','2025-07-18 06:54:45','2025-07-18 06:54:45'),('ZL20210907182025317',1011,2002,'','available','2025-07-18 07:11:06','2025-07-18 07:11:06'),('ZL20210907182057663',1007,10,'','available','2025-07-18 06:31:40','2025-07-18 06:31:40'),('ZL20210913183517050',1011,2002,'','available','2025-07-18 07:13:32','2025-07-18 07:13:32'),('ZL20210913183559557',1011,2002,'','available','2025-07-18 07:14:05','2025-07-18 07:14:05'),('ZL20210913183612657',1011,2002,'','available','2025-07-18 07:10:01','2025-07-18 07:10:01'),('ZL20210913183655710',1007,10,'','available','2025-07-18 06:28:03','2025-07-18 06:28:03'),('ZL20210913183835960',1007,10,'','available','2025-07-18 06:29:58','2025-07-18 06:29:58'),('ZL20210930101123970',1007,10,'','available','2025-07-18 06:30:22','2025-07-18 06:30:22'),('ZL20210930101145410',1011,2002,'','available','2025-07-18 07:12:12','2025-07-18 07:12:12'),('ZL20210930101230760',1007,11,'','available','2025-07-18 06:39:16','2025-07-18 06:39:16'),('ZL20210930101514590',1007,12,'','available','2025-07-18 06:52:29','2025-07-18 06:52:29'),('ZL20211014171859087',1007,12,'','available','2025-07-18 06:53:07','2025-07-18 06:53:07'),('ZL20211014171912713',1007,19,'','available','2025-07-18 09:24:18','2025-07-18 09:24:18'),('ZL20211014171952460',1007,9,'','available','2025-07-18 06:17:50','2025-07-18 06:17:50'),('ZL20211014172153370',1007,10,'','available','2025-07-18 06:25:26','2025-07-18 06:25:26'),('ZL20211014172249397',1007,9,'','available','2025-07-18 06:17:22','2025-07-18 06:17:22'),('ZL20211014172357720',1007,17,'','available','2025-07-18 08:12:59','2025-07-18 08:12:59'),('ZL20211014172400603',1007,17,'','available','2025-07-18 08:13:12','2025-07-18 08:13:12'),('ZL20211118143122590',1011,2002,'','available','2025-07-18 07:13:49','2025-07-18 07:13:49'),('ZL20211118143307697',1007,17,'','available','2025-07-18 08:12:10','2025-07-18 08:12:10'),('ZL20211118143403777',1007,11,'','available','2025-07-18 06:40:05','2025-07-18 06:40:05'),('ZL20211118143531943',1011,2002,'','available','2025-07-18 07:14:19','2025-07-18 07:14:19'),('ZL20211118143534937',1007,12,'','available','2025-07-18 06:55:38','2025-07-18 06:55:38'),('ZL20211118143539260',1007,19,'','available','2025-07-18 09:25:26','2025-07-18 09:25:26'),('ZL20211118143542777',1011,2002,'','available','2025-07-18 07:12:30','2025-07-18 07:12:30'),('ZL20211118144421917',1007,12,'','available','2025-07-18 06:52:06','2025-07-18 06:52:06'),('ZL20211118144512800',1007,10,'','available','2025-07-18 06:30:44','2025-07-18 06:30:44'),('ZL20211118144600277',1007,11,'','available','2025-07-18 06:39:41','2025-07-18 06:39:41'),('ZL20220110104226393',1007,11,'','available','2025-07-18 06:40:26','2025-07-18 06:40:26'),('ZL20220110104226847',1007,11,'','available','2025-07-18 06:41:12','2025-07-18 06:41:12'),('ZL20220110104233690',1007,17,'','available','2025-07-18 08:16:35','2025-07-18 08:16:35'),('ZL20220110104238853',1007,12,'','available','2025-07-18 06:54:31','2025-07-18 06:54:31'),('ZL20220110104240027',1007,10,'','available','2025-07-18 06:28:35','2025-07-18 06:28:35'),('ZL20220110104243587',1011,2002,'','available','2025-07-18 07:08:59','2025-07-18 07:08:59'),('ZL20220110104252453',1007,11,'','available','2025-07-18 06:42:04','2025-07-18 06:42:04'),('ZL20220110104253847',1007,11,'','available','2025-07-18 06:38:56','2025-07-18 06:38:56'),('ZL20220110104254063',1007,17,'','available','2025-07-18 08:11:52','2025-07-18 08:11:52'),('ZL20220110104255460',1007,17,'','available','2025-07-18 08:16:04','2025-07-18 08:16:04'),('ZL20220110104306990',1007,17,'','available','2025-07-18 08:10:56','2025-07-18 08:10:56'),('ZL20220110104315627',1007,9,'','available','2025-07-18 06:16:21','2025-07-18 06:16:21'),('ZL20220110104329383',1007,9,'','available','2025-07-18 06:14:09','2025-07-18 06:14:09'),('ZL20220110104404070',1007,9,'','available','2025-07-18 06:15:05','2025-07-18 06:15:05'),('ZL20220110104419427',1007,9,'','available','2025-07-18 06:11:44','2025-07-18 06:11:44'),('ZL20220110104535033',1007,12,'','available','2025-07-18 06:53:52','2025-07-18 06:53:52'),('ZL20220111163314007',1007,9,'','available','2025-07-18 06:10:01','2025-07-18 06:10:01'),('ZL20220111163320450',1007,10,'','available','2025-07-18 06:29:28','2025-07-18 06:29:28'),('ZL20220111163337863',1007,19,'','available','2025-07-18 09:25:59','2025-07-18 09:25:59'),('ZL20220111163343757',1007,12,'','available','2025-07-18 06:53:29','2025-07-18 06:53:29'),('ZL20220111163357667',1007,17,'','available','2025-07-18 08:11:22','2025-07-18 08:11:22'),('ZL20220111163359763',1007,17,'','available','2025-07-18 08:12:42','2025-07-18 08:12:42'),('ZL20220111163423653',1007,10,'','available','2025-07-18 06:28:58','2025-07-18 06:28:58'),('ZL20220111163435780',1007,10,'','available','2025-07-18 06:32:07','2025-07-18 06:32:07'),('ZL20220111163439730',1007,9,'','available','2025-07-18 06:17:01','2025-07-18 06:17:01'),('ZL20220111163449413',1007,12,'','available','2025-07-18 06:54:10','2025-07-18 06:54:10'),('ZL20220111163503880',1007,11,'','available','2025-07-18 06:38:05','2025-07-18 06:38:05'),('ZL20220111163504493',1011,2002,'','available','2025-07-18 07:10:27','2025-07-18 07:10:27');
/*!40000 ALTER TABLE `Device` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `DeviceType`
--

DROP TABLE IF EXISTS `DeviceType`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `DeviceType` (
  `type_id` int NOT NULL AUTO_INCREMENT,
  `type_name` varchar(50) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`type_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1017 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `DeviceType`
--

LOCK TABLES `DeviceType` WRITE;
/*!40000 ALTER TABLE `DeviceType` DISABLE KEYS */;
INSERT INTO `DeviceType` VALUES (1007,'面包车','2025-07-13 13:48:31','2025-07-13 13:48:31'),(1008,'平板车500','2025-07-18 01:59:32','2025-07-18 01:59:32'),(1009,'箱货','2025-07-18 02:00:01','2025-07-18 02:29:55'),(1010,'面包车200','2025-07-18 02:48:14','2025-07-18 02:48:14'),(1011,'怀疑坏的','2025-07-18 07:07:40','2025-07-18 07:07:40'),(1012,'面包车单北斗500','2025-07-18 07:34:33','2025-07-18 07:34:33'),(1013,'平板车700','2025-07-18 07:47:07','2025-07-18 07:47:07');
/*!40000 ALTER TABLE `DeviceType` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `OutboundRecord`
--

DROP TABLE IF EXISTS `OutboundRecord`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `OutboundRecord` (
  `record_id` int NOT NULL AUTO_INCREMENT COMMENT '出库记录ID',
  `device_id` varchar(30) NOT NULL,
  `user_id` int NOT NULL COMMENT '领用人ID',
  `outbound_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `expected_return_date` date DEFAULT NULL,
  `actual_return_date` date DEFAULT NULL,
  `purpose` text,
  `status` enum('outbound','returned') DEFAULT 'outbound',
  `notes` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`record_id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `OutboundRecord_ibfk_1` FOREIGN KEY (`device_id`) REFERENCES `Device` (`device_id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `OutboundRecord_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `User` (`user_id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='出库记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `OutboundRecord`
--

LOCK TABLES `OutboundRecord` WRITE;
/*!40000 ALTER TABLE `OutboundRecord` DISABLE KEYS */;
/*!40000 ALTER TABLE `OutboundRecord` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `StorageLocation`
--

DROP TABLE IF EXISTS `StorageLocation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `StorageLocation` (
  `location_id` int NOT NULL AUTO_INCREMENT,
  `capacity` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`location_id`),
  CONSTRAINT `StorageLocation_chk_1` CHECK ((`capacity` >= 0))
) ENGINE=InnoDB AUTO_INCREMENT=2006 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `StorageLocation`
--

LOCK TABLES `StorageLocation` WRITE;
/*!40000 ALTER TABLE `StorageLocation` DISABLE KEYS */;
INSERT INTO `StorageLocation` VALUES (1,10,'2025-07-09 06:04:55','2025-07-22 15:18:07'),(2,10,'2025-07-07 15:49:04','2025-07-07 15:49:04'),(3,10,'2025-07-07 15:49:38','2025-07-07 15:49:38'),(4,10,'2025-07-07 15:49:55','2025-07-07 15:49:55'),(5,10,'2025-07-07 15:50:07','2025-07-07 15:50:07'),(6,10,'2025-07-07 15:50:20','2025-07-07 15:50:20'),(7,10,'2025-07-07 15:50:28','2025-07-07 15:50:28'),(8,10,'2025-07-07 15:50:35','2025-07-07 15:50:35'),(9,10,'2025-07-07 15:50:44','2025-07-07 15:50:44'),(10,10,'2025-07-07 15:50:56','2025-07-07 15:50:56'),(11,10,'2025-07-18 06:35:27','2025-07-18 06:35:27'),(12,10,'2025-07-18 06:35:35','2025-07-18 06:35:35'),(13,15,'2025-07-18 06:35:39','2025-07-18 07:38:18'),(14,7,'2025-07-18 06:35:43','2025-07-18 07:53:28'),(15,16,'2025-07-18 06:36:40','2025-07-18 07:53:16'),(16,5,'2025-07-18 06:37:00','2025-07-18 08:09:45'),(17,10,'2025-07-18 06:37:07','2025-07-18 06:37:07'),(18,10,'2025-07-18 06:37:15','2025-07-18 06:37:15'),(19,20,'2025-07-18 06:37:18','2025-07-27 10:24:22'),(20,10,'2025-07-18 06:37:22','2025-07-18 06:37:22'),(21,10,'2025-07-27 10:33:25','2025-07-27 10:33:25'),(22,10,'2025-08-05 10:48:46','2025-08-05 10:51:04'),(2002,29,'2025-07-18 07:08:17','2025-07-18 07:18:19');
/*!40000 ALTER TABLE `StorageLocation` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `User`
--

DROP TABLE IF EXISTS `User`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `User` (
  `user_id` int NOT NULL AUTO_INCREMENT COMMENT '领用人ID',
  `name` varchar(100) DEFAULT NULL,
  `department` varchar(100) DEFAULT NULL,
  `contact` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='领用人表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `User`
--

LOCK TABLES `User` WRITE;
/*!40000 ALTER TABLE `User` DISABLE KEYS */;
INSERT INTO `User` VALUES (2,'临海',NULL,NULL,'2025-07-22 06:50:35','2025-07-22 14:21:31'),(3,'温岭',NULL,NULL,'2025-07-22 06:50:35','2025-07-22 14:21:31'),(8,'台州',NULL,NULL,'2025-08-04 14:39:37','2025-08-04 14:39:37');
/*!40000 ALTER TABLE `User` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-08-14 22:18:40
