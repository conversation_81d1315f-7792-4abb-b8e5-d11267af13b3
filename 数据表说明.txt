数据库表格文件说明
==================

本目录包含从 anxinlaDB_backup_20250814_221840.sql 备份文件中提取的数据表格：

1. Admin_数据表.xlsx
   - 管理员信息表
   - 包含 1 行数据
   - 字段：admin_id, username, password, created_at, updated_at

2. Device_数据表.xlsx  
   - 设备信息表
   - 包含 239 行数据
   - 字段：device_id, device_type, storage_location, description, status, created_at, updated_at
   - 记录了所有设备的详细信息，包括设备ID、类型、存储位置、状态等

3. DeviceType_数据表.xlsx
   - 设备类型表
   - 包含 7 行数据  
   - 字段：type_id, type_name, created_at, updated_at
   - 定义了设备的分类，如面包车、平板车、箱货等

4. StorageLocation_数据表.xlsx
   - 存储位置表
   - 包含 23 行数据
   - 字段：location_id, capacity, created_at, updated_at
   - 记录了各个存储位置的容量信息

5. User_数据表.xlsx
   - 用户信息表
   - 包含 3 行数据
   - 字段：user_id, name, department, contact, created_at, updated_at
   - 记录了系统用户的基本信息

注意：
- OutboundRecord（出库记录表）在备份文件中没有数据，因此未生成对应的Excel文件
- 所有Excel文件都包含列标题，便于查看和分析
- 时间字段保持原始格式，可根据需要进行格式化

生成时间：2025-08-14
工具：sql_to_excel.py
