#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQL备份文件转Excel表格工具
将MySQL备份文件中的数据提取并转换为Excel文件
"""

import re
import pandas as pd
import os
from datetime import datetime

def parse_sql_file(sql_file_path):
    """解析SQL文件，提取表结构和数据"""
    tables = {}
    current_table = None

    with open(sql_file_path, 'r', encoding='utf-8') as file:
        content = file.read()

    # 查找所有CREATE TABLE语句
    create_table_pattern = r'CREATE TABLE `(\w+)` \((.*?)\) ENGINE='
    create_matches = re.findall(create_table_pattern, content, re.DOTALL)

    for table_name, table_def in create_matches:
        # 解析列定义 - 更精确的解析
        columns = []
        # 按行分割表定义
        lines = table_def.split('\n')
        for line in lines:
            line = line.strip()
            # 匹配列定义行（以反引号开始）
            if line.startswith('`') and not line.startswith('PRIMARY') and not line.startswith('KEY') and not line.startswith('CONSTRAINT'):
                # 提取列名
                match = re.match(r'`(\w+)`', line)
                if match:
                    col_name = match.group(1)
                    # 排除索引和约束相关的行
                    if col_name not in ['PRIMARY', 'KEY', 'CONSTRAINT', 'UNIQUE', 'INDEX']:
                        columns.append(col_name)

        tables[table_name] = {'columns': columns, 'data': []}
    
    # 查找所有INSERT INTO语句
    insert_pattern = r'INSERT INTO `(\w+)` VALUES (.*?);'
    insert_matches = re.findall(insert_pattern, content, re.DOTALL)
    
    for table_name, values_str in insert_matches:
        if table_name in tables:
            # 解析VALUES中的数据
            # 处理复杂的VALUES语句，支持多行插入
            values_str = values_str.strip()
            
            # 使用正则表达式匹配每个值组
            value_groups = []
            current_pos = 0
            paren_count = 0
            start_pos = -1
            in_string = False
            escape_next = False
            
            for i, char in enumerate(values_str):
                if escape_next:
                    escape_next = False
                    continue
                    
                if char == '\\':
                    escape_next = True
                    continue
                    
                if char == "'" and not escape_next:
                    in_string = not in_string
                    continue
                    
                if not in_string:
                    if char == '(':
                        if paren_count == 0:
                            start_pos = i
                        paren_count += 1
                    elif char == ')':
                        paren_count -= 1
                        if paren_count == 0 and start_pos != -1:
                            value_group = values_str[start_pos+1:i]
                            value_groups.append(value_group)
                            start_pos = -1
            
            # 解析每个值组
            for value_group in value_groups:
                row_data = []
                values = []
                current_value = ""
                in_string = False
                escape_next = False
                paren_count = 0
                
                for char in value_group:
                    if escape_next:
                        current_value += char
                        escape_next = False
                        continue
                        
                    if char == '\\':
                        current_value += char
                        escape_next = True
                        continue
                        
                    if char == "'" and paren_count == 0:
                        if not in_string:
                            in_string = True
                        else:
                            in_string = False
                        continue
                        
                    if char == '(' and not in_string:
                        paren_count += 1
                        current_value += char
                        continue
                        
                    if char == ')' and not in_string:
                        paren_count -= 1
                        current_value += char
                        continue
                        
                    if char == ',' and not in_string and paren_count == 0:
                        values.append(current_value.strip())
                        current_value = ""
                        continue
                        
                    current_value += char
                
                if current_value.strip():
                    values.append(current_value.strip())
                
                # 清理和转换值
                cleaned_values = []
                for value in values:
                    value = value.strip()
                    if value == 'NULL':
                        cleaned_values.append(None)
                    elif value.startswith("'") and value.endswith("'"):
                        cleaned_values.append(value[1:-1])
                    else:
                        cleaned_values.append(value)
                
                tables[table_name]['data'].append(cleaned_values)
    
    return tables

def create_excel_files(tables, output_dir='.'):
    """为每个表创建一个Excel文件"""
    created_files = []

    for table_name, table_info in tables.items():
        if not table_info['data']:  # 跳过空表
            print(f"跳过空表: {table_name}")
            continue

        # 检查数据和列数是否匹配
        expected_cols = len(table_info['columns'])
        for i, row in enumerate(table_info['data']):
            if len(row) != expected_cols:
                print(f"警告: 表 {table_name} 第 {i+1} 行数据列数 ({len(row)}) 与表结构列数 ({expected_cols}) 不匹配")
                # 调整行数据长度
                if len(row) < expected_cols:
                    # 补充空值
                    row.extend([None] * (expected_cols - len(row)))
                else:
                    # 截断多余的列
                    table_info['data'][i] = row[:expected_cols]

        # 创建DataFrame
        df = pd.DataFrame(table_info['data'], columns=table_info['columns'])
        
        # 生成文件名
        filename = f"{table_name}_数据表.xlsx"
        filepath = os.path.join(output_dir, filename)
        
        # 保存为Excel文件
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name=table_name, index=False)
            
            # 调整列宽
            worksheet = writer.sheets[table_name]
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
        
        created_files.append(filepath)
        print(f"已创建: {filename} (包含 {len(df)} 行数据)")
    
    return created_files

def main():
    """主函数"""
    sql_file = "anxinlaDB_backup_20250814_221840.sql"
    
    if not os.path.exists(sql_file):
        print(f"错误: 找不到SQL文件 {sql_file}")
        return
    
    print("开始解析SQL文件...")
    tables = parse_sql_file(sql_file)
    
    print(f"发现 {len(tables)} 个表:")
    for table_name, table_info in tables.items():
        print(f"  - {table_name}: {len(table_info['columns'])} 列, {len(table_info['data'])} 行数据")
    
    print("\n开始创建Excel文件...")
    created_files = create_excel_files(tables)
    
    print(f"\n完成! 共创建了 {len(created_files)} 个Excel文件:")
    for file in created_files:
        print(f"  - {os.path.basename(file)}")

if __name__ == "__main__":
    main()
